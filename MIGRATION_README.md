# Squad Structure Migration

## Overview

This migration transforms the squad database structure from a single collection with member arrays to a dual-reference subcollection structure for better performance and scalability.

## Migration Changes

### Before (Current Structure)
```
/squads/{squadId}
{
  id: string
  name: string
  description?: string
  leaderId: string
  members: string[] // Array of user IDs
  createdAt: Timestamp
}
```

### After (New Structure)
```
/squads/{squadId}
{
  id: string
  name: string
  description?: string
  leaderId: string
  memberCount: number // Total member count
  createdAt: Timestamp
}

/squads/{squadId}/members/{userId}
{
  userId: string
  joinedAt: Timestamp
  role: 'leader' | 'member'
  invitedBy?: string
  joinMethod: 'email_invitation' | 'shareable_link' | 'legacy_invitation'
}

/users/{userId}/squads/{squadId}
{
  squadId: string
  squadName: string
  role: 'leader' | 'member'
  joinedAt: Timestamp
  lastActivity?: Timestamp
}
```

## Benefits

1. **Performance**: Eliminates N+1 query problems
2. **Scalability**: No document size limits for large squads
3. **Real-time Efficiency**: Better subscription patterns
4. **Concurrent Operations**: Multiple users can join/leave simultaneously

## Migration Steps

### 1. Install Dependencies
```bash
npm install tsx
```

### 2. Run Migration Script
```bash
npm run migrate:squad-structure
```

### 3. Verify Migration
The script will automatically verify:
- All squads migrated successfully
- Member subcollections created
- User squad subcollections created
- Data consistency maintained

## Code Changes Made

### Services Updated
- ✅ `lib/domains/squad/squad.service.ts` - Core CRUD operations
- ✅ `lib/domains/squad/squad.realtime.service.ts` - Real-time subscriptions
- ✅ `lib/firebase/squad-service.ts` - Legacy compatibility layer

### Stores Updated
- ✅ `lib/domains/squad/squad.store.ts` - State management
- ✅ `lib/domains/squad/squad.hooks.ts` - React hooks
- ✅ `lib/domains/squad/squad.realtime.hooks.ts` - Real-time hooks

### Components Updated
- ✅ Dashboard squad display
- ✅ Squad pages and member management
- ✅ Trip creation and management
- ✅ Invitation system
- ✅ AI suggestion components

### Security Rules Updated
- ✅ `firestore.rules` - New subcollection permissions
- ✅ `firestore.indexes.json` - Optimized indexes

## Breaking Changes

### API Changes
- `squad.members` array removed, use `squad.memberCount` for count
- New hooks: `useSquadMembersData()`, `useRealtimeSquadMembers()`
- Updated service methods for member management

### Component Props
- Components expecting `squad.members` updated to use new structure
- Trip creation now populates attendees after creation

## Rollback Plan

If issues occur, the migration script creates a backup of the original data structure. Contact the development team for rollback procedures.

## Performance Improvements Expected

- **Member Queries**: 60-80% faster
- **Real-time Updates**: 40-50% more efficient  
- **Page Load Times**: 30-50% improvement for squad pages
- **Scalability**: Support for unlimited squad sizes

## Testing

After migration, test:
1. Squad creation and deletion
2. Member addition and removal
3. Real-time updates
4. Trip creation with squads
5. Invitation system functionality

## Support

For issues or questions about the migration, contact the development team.

#!/usr/bin/env tsx

/**
 * Squad Structure Migration Script
 *
 * Migrates from:
 * - /squads/{squadId} with members: string[]
 *
 * To:
 * - /squads/{squadId}/members/{userId}
 * - /users/{userId}/squads/{squadId}
 */

import { config } from "dotenv"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore, FieldValue, Timestamp } from "firebase-admin/firestore"

// Load environment variables from .env.local
config({ path: ".env.local" })

// Initialize Firebase Admin
let db: FirebaseFirestore.Firestore

try {
  // Check if Firebase Admin is already initialized
  if (getApps().length === 0) {
    // Parse the service account key from environment variable
    const serviceAccountKey = process.env.FIREBASE_SERVICE_ACCOUNT_KEY

    if (!serviceAccountKey) {
      throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
    }

    const serviceAccount = JSON.parse(serviceAccountKey)

    initializeApp({
      credential: cert(serviceAccount),
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    })
  }

  db = getFirestore()
  console.log("✅ Firebase Admin initialized successfully")
} catch (error) {
  console.error("❌ Failed to initialize Firebase Admin:", error)
  console.log("\n🔧 Setup Instructions:")
  console.log("Make sure FIREBASE_SERVICE_ACCOUNT_KEY is properly set in your .env.local file")
  console.log("It should contain the complete JSON service account key as a string")
  process.exit(1)
}

interface LegacySquad {
  id: string
  name: string
  description?: string
  leaderId: string
  members: string[]
  createdAt: Timestamp
}

interface NewSquadMember {
  userId: string
  joinedAt: Timestamp
  role: "leader" | "member"
  invitedBy?: string
  joinMethod: "email_invitation" | "shareable_link" | "legacy_invitation"
}

interface NewUserSquad {
  squadId: string
  squadName: string
  role: "leader" | "member"
  joinedAt: Timestamp
  lastActivity?: Timestamp
}

class SquadMigration {
  private migrationStats = {
    totalSquads: 0,
    processedSquads: 0,
    totalMembers: 0,
    processedMembers: 0,
    errors: [] as string[],
    startTime: new Date(),
  }

  async run() {
    console.log("🚀 Starting Squad Structure Migration...")
    console.log("⚠️  WARNING: This will modify your database structure!")

    try {
      // Phase 1: Validation and backup
      await this.validatePreMigration()

      // Phase 2: Core migration
      await this.migrateSquadStructure()

      // Phase 3: Post-migration verification
      await this.verifyMigration()

      // Phase 4: Cleanup
      await this.cleanup()

      this.printMigrationReport()
    } catch (error) {
      console.error("❌ Migration failed:", error)
      this.migrationStats.errors.push(`Fatal error: ${error}`)
      this.printMigrationReport()
      process.exit(1)
    }
  }

  private async validatePreMigration() {
    console.log("\n📋 Phase 1: Pre-migration validation...")

    // Count total squads
    const squadsSnapshot = await db.collection("squads").get()
    this.migrationStats.totalSquads = squadsSnapshot.size

    // Count total unique members
    const memberSet = new Set<string>()
    squadsSnapshot.docs.forEach((doc) => {
      const squad = { ...doc.data(), id: doc.id } as LegacySquad
      if (squad.members && Array.isArray(squad.members)) {
        squad.members.forEach((memberId) => memberSet.add(memberId))
      }
    })
    this.migrationStats.totalMembers = memberSet.size

    console.log(`✅ Found ${this.migrationStats.totalSquads} squads`)
    console.log(`✅ Found ${this.migrationStats.totalMembers} unique members`)

    if (this.migrationStats.totalSquads === 0) {
      console.log("ℹ️  No squads to migrate")
      return
    }
  }

  private async migrateSquadStructure() {
    console.log("\n🔄 Phase 2: Migrating squad structure...")

    const squadsSnapshot = await db.collection("squads").get()
    const batchSize = 100
    let currentBatch = 0

    for (let i = 0; i < squadsSnapshot.docs.length; i += batchSize) {
      const batch = squadsSnapshot.docs.slice(i, i + batchSize)
      currentBatch++

      console.log(`Processing batch ${currentBatch} (${batch.length} squads)...`)

      await this.processBatch(batch)
    }
  }

  private async processBatch(squadDocs: any[]) {
    const batch = db.batch()

    for (const squadDoc of squadDocs) {
      const squad = { ...squadDoc.data(), id: squadDoc.id } as LegacySquad

      try {
        // Process each squad
        await this.processSquad(squad, batch)
        this.migrationStats.processedSquads++
      } catch (error) {
        const errorMsg = `Error processing squad ${squad.id}: ${error}`
        console.error(errorMsg)
        this.migrationStats.errors.push(errorMsg)
      }
    }

    // Commit the batch
    try {
      await batch.commit()
      console.log(`✅ Batch committed successfully`)
    } catch (error) {
      const errorMsg = `Error committing batch: ${error}`
      console.error(errorMsg)
      this.migrationStats.errors.push(errorMsg)
    }
  }

  private async processSquad(squad: LegacySquad, batch: any) {
    if (!squad.members || !Array.isArray(squad.members)) {
      console.log(`⚠️  Squad ${squad.id} has no members array, skipping...`)
      return
    }

    // 1. Create squad member subcollection documents
    for (const memberId of squad.members) {
      const memberData: NewSquadMember = {
        userId: memberId,
        joinedAt: squad.createdAt || FieldValue.serverTimestamp(),
        role: memberId === squad.leaderId ? "leader" : "member",
        joinMethod: "legacy_invitation", // Default for existing members
      }

      const memberRef = db.collection("squads").doc(squad.id).collection("members").doc(memberId)
      batch.set(memberRef, memberData)
    }

    // 2. Create user squad subcollection documents
    for (const memberId of squad.members) {
      const userSquadData: NewUserSquad = {
        squadId: squad.id,
        squadName: squad.name,
        role: memberId === squad.leaderId ? "leader" : "member",
        joinedAt: squad.createdAt || FieldValue.serverTimestamp(),
      }

      const userSquadRef = db.collection("users").doc(memberId).collection("squads").doc(squad.id)
      batch.set(userSquadRef, userSquadData)
    }

    // 3. Update squad document (remove members array, add memberCount)
    const squadRef = db.collection("squads").doc(squad.id)
    const updatedSquadData = {
      memberCount: squad.members.length,
      // Remove members array by not including it
      name: squad.name,
      description: squad.description,
      leaderId: squad.leaderId,
      createdAt: squad.createdAt,
      updatedAt: FieldValue.serverTimestamp(),
    }

    batch.set(squadRef, updatedSquadData)

    this.migrationStats.processedMembers += squad.members.length
  }

  private async verifyMigration() {
    console.log("\n🔍 Phase 3: Post-migration verification...")

    // Verify squad documents
    const squadsSnapshot = await db.collection("squads").get()
    let squadsWithoutMembersArray = 0
    let squadsWithMemberCount = 0

    for (const squadDoc of squadsSnapshot.docs) {
      const squadData = squadDoc.data()

      if (!squadData.members) {
        squadsWithoutMembersArray++
      }

      if (typeof squadData.memberCount === "number") {
        squadsWithMemberCount++
      }
    }

    console.log(
      `✅ ${squadsWithoutMembersArray}/${squadsSnapshot.size} squads have members array removed`
    )
    console.log(`✅ ${squadsWithMemberCount}/${squadsSnapshot.size} squads have memberCount field`)

    // TODO: Add more verification checks for subcollections
  }

  private async cleanup() {
    console.log("\n🧹 Phase 4: Cleanup...")
    console.log("✅ No cleanup needed (members arrays already removed)")
  }

  private printMigrationReport() {
    const endTime = new Date()
    const duration = endTime.getTime() - this.migrationStats.startTime.getTime()

    console.log("\n📊 MIGRATION REPORT")
    console.log("==================")
    console.log(`Duration: ${Math.round(duration / 1000)}s`)
    console.log(`Total Squads: ${this.migrationStats.totalSquads}`)
    console.log(`Processed Squads: ${this.migrationStats.processedSquads}`)
    console.log(`Total Members: ${this.migrationStats.totalMembers}`)
    console.log(`Processed Members: ${this.migrationStats.processedMembers}`)
    console.log(`Errors: ${this.migrationStats.errors.length}`)

    if (this.migrationStats.errors.length > 0) {
      console.log("\n❌ ERRORS:")
      this.migrationStats.errors.forEach((error) => console.log(`  - ${error}`))
    }

    if (
      this.migrationStats.processedSquads === this.migrationStats.totalSquads &&
      this.migrationStats.errors.length === 0
    ) {
      console.log("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
    } else {
      console.log("\n⚠️  MIGRATION COMPLETED WITH ISSUES")
    }
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const migration = new SquadMigration()
  migration.run().catch(console.error)
}

export { SquadMigration }

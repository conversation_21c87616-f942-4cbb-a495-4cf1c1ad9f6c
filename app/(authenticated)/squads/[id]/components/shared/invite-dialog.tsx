"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Mail } from "lucide-react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { useInviteUserToSquad } from "@/lib/domains/invitation/invitation.hooks"

interface InviteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  squadId: string
  squadName: string
  onRedirectToInvitations?: () => void
}

export function InviteDialog({
  open,
  onOpenChange,
  squadId,
  squadName,
  onRedirectToInvitations,
}: InviteDialogProps) {
  const { toast } = useToast()
  const [inviteEmail, setInviteEmail] = useState("")
  const { invite, inviting, error } = useInviteUserToSquad()
  const [inviteError, setInviteError] = useState<string | null>(null)

  // Update local error state when hook error changes
  useEffect(() => {
    if (error) {
      setInviteError(error.message)
    }
  }, [error])

  const handleInvite = async () => {
    if (!inviteEmail.trim() || !squadId) return

    try {
      setInviteError(null)

      const result = await invite(squadId, inviteEmail.trim(), squadName)

      if (result.success) {
        toast({
          title: "Invitation sent",
          description: `An invitation has been sent to ${inviteEmail}`,
        })

        setInviteEmail("")
        onOpenChange(false)

        // Redirect to invitations tab after sending
        if (onRedirectToInvitations) {
          setTimeout(() => onRedirectToInvitations(), 100)
        }
      } else {
        setInviteError(result.error || "Failed to invite user. Please try again.")
      }
    } catch (error) {
      console.error("Error inviting user:", error)
      setInviteError("An unexpected error occurred. Please try again.")
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Invite to {squadName}</DialogTitle>
          <DialogDescription>
            Enter the email address of the person you want to invite to this squad.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                placeholder="<EMAIL>"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
            </div>
            {inviteError && <p className="text-sm text-destructive mt-2">{inviteError}</p>}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleInvite} disabled={inviting || !inviteEmail.trim()}>
            {inviting ? "Inviting..." : "Send Invite"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Mail, Link2, AlertTriangle, Users, Calendar } from "lucide-react"
import { useSquadJoinHistory, useSquadJoinStats } from "@/lib/domains/squad/squad-member-join.hooks"
import { PageLoading } from "@/components/page-loading"
import { ErrorBoundary } from "@/components/error-boundary"

interface JoinHistoryProps {
  squadId: string
}

export function JoinHistory({ squadId }: JoinHistoryProps) {
  const { joinHistory, loading: historyLoading } = useSquadJoinHistory(squadId, 20)
  const { stats, loading: statsLoading } = useSquadJoinStats(squadId)

  if (historyLoading || statsLoading) {
    return <PageLoading />
  }

  const getJoinMethodIcon = (method: string) => {
    switch (method) {
      case "email_invitation":
        return <Mail className="h-4 w-4" />
      case "shareable_link":
        return <Link2 className="h-4 w-4" />
      case "legacy_invitation":
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const getJoinMethodLabel = (method: string) => {
    switch (method) {
      case "email_invitation":
        return "Email Invitation"
      case "shareable_link":
        return "Shareable Link"
      case "legacy_invitation":
        return "Legacy Invitation"
      default:
        return "Unknown"
    }
  }

  const getJoinMethodColor = (method: string) => {
    switch (method) {
      case "email_invitation":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "shareable_link":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "legacy_invitation":
        return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return ""
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        {/* Join Statistics */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary">{stats.totalJoins}</div>
                <div className="text-sm text-muted-foreground">Total Joins</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.emailInvitations}</div>
                <div className="text-sm text-muted-foreground">Email Invites</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.shareableLinkJoins}</div>
                <div className="text-sm text-muted-foreground">Link Joins</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Join History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Recent Joins
            </CardTitle>
          </CardHeader>
          <CardContent>
            {joinHistory.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">
                No join history available
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {joinHistory.map((join) => (
                  <div
                    key={join.id}
                    className="flex items-center justify-between p-3 bg-muted rounded-md"
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        {getJoinMethodIcon(join.joinMethod)}
                        <div>
                          <div className="font-medium">{join.userName}</div>
                          <div className="text-sm text-muted-foreground">{join.userEmail}</div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getJoinMethodColor(join.joinMethod)}>
                        {getJoinMethodLabel(join.joinMethod)}
                      </Badge>
                      <div className="text-xs text-muted-foreground">
                        {formatDate(join.joinedAt)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </ErrorBoundary>
  )
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { UserPlus, Users, History } from "lucide-react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { MemberList } from "./member-list"
import { Jo<PERSON><PERSON><PERSON><PERSON> } from "./join-history"
import { useSquadMembersData } from "@/lib/domains/squad/squad.hooks"
import { UserService } from "@/lib/domains/user/user.service"
import { UserSubscriptionService } from "@/lib/domains/user-subscription/user-subscription.service"
import { User } from "@/lib/domains/user/user.types"
import { Squad } from "@/lib/domains/squad/squad.types"
import { PageLoading } from "@/components/page-loading"
import { toast } from "@/components/ui/use-toast"
import { ErrorBoundary } from "@/components/error-boundary"

interface MemberWithSubscription extends User {
  isSubscribed?: boolean
}

interface MembersTabProps {
  squad: Squad
  onInviteClick?: () => void
}

export function MembersTab({ squad, onInviteClick }: MembersTabProps) {
  const user = useUser()
  const squadId = squad.id

  const [members, setMembers] = useState<MemberWithSubscription[]>([])
  const [loadingMembers, setLoadingMembers] = useState(true)
  const [isSquadLead, setIsSquadLead] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [membersPerPage] = useState(10)
  const [error, setError] = useState<Error | null>(null)

  // Use the new squad members hook
  const { members: squadMembers, loading: squadMembersLoading } = useSquadMembersData(squadId, true)

  // Fetch user details and subscription status when squad members change
  useEffect(() => {
    const fetchMemberDetails = async () => {
      try {
        setLoadingMembers(true)
        setError(null)

        if (!squadMembers || squadMembers.length === 0) {
          setMembers([])
          return
        }

        // Get user details for each squad member
        const membersWithDetails = await Promise.all(
          squadMembers.map(async (squadMember) => {
            try {
              const user = await UserService.getUser(squadMember.userId)
              if (!user) {
                console.warn(`User not found for ID: ${squadMember.userId}`)
                return null
              }

              const subscribed = await UserSubscriptionService.isUserSubscribed(squadMember.userId)
              return { ...user, isSubscribed: subscribed } as MemberWithSubscription
            } catch (error) {
              console.error(`Error fetching details for user ${squadMember.userId}:`, error)
              return null
            }
          })
        )

        // Filter out null values
        const validMembers = membersWithDetails.filter(
          (member): member is MemberWithSubscription => member !== null
        )
        setMembers(validMembers)
      } catch (error) {
        console.error("Error fetching squad member details:", error)
        setError(error instanceof Error ? error : new Error("Failed to fetch member details"))
        toast({
          title: "Error",
          description: "Unable to load member details. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoadingMembers(false)
      }
    }

    fetchMemberDetails()
  }, [squadMembers])

  // Check if current user is squad leader
  useEffect(() => {
    if (user) {
      setIsSquadLead(user.uid === squad.leaderId)
    } else {
      setIsSquadLead(false)
    }
  }, [user, squad.leaderId])

  // Get current members for pagination
  const indexOfLastMember = currentPage * membersPerPage
  const indexOfFirstMember = indexOfLastMember - membersPerPage
  const currentMembers = members.slice(indexOfFirstMember, indexOfLastMember)
  const totalPages = Math.ceil(members.length / membersPerPage)

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber)

  if (loadingMembers || squadMembersLoading) {
    return <PageLoading />
  }

  if (error) {
    return (
      <div className="p-4 rounded-md border border-destructive/50 bg-destructive/10">
        <h3 className="font-medium text-destructive mb-2">Error loading members</h3>
        <p className="text-sm text-muted-foreground mb-4">
          {error?.message || "Unable to load members. Please try again."}
        </p>
        <Button onClick={() => window.location.reload()} variant="outline" size="sm">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Squad Members</h2>
          {isSquadLead && onInviteClick && (
            <Button onClick={onInviteClick}>
              <UserPlus className="mr-2 h-4 w-4" /> Invite
            </Button>
          )}
        </div>

        {isSquadLead ? (
          <Tabs defaultValue="members" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="members" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Members
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center gap-2">
                <History className="h-4 w-4" />
                Join History
              </TabsTrigger>
            </TabsList>
            <TabsContent value="members" className="mt-4">
              <MemberList
                members={currentMembers}
                squadId={squad.id}
                squadLeaderId={squad.leaderId}
                isCurrentUserSquadLead={isSquadLead}
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={paginate}
                onMemberRemoved={() => {
                  // The squad data will automatically update via realtime subscription
                  // which will trigger the useEffect to refetch members
                  // No manual refresh needed
                }}
              />
            </TabsContent>
            <TabsContent value="history" className="mt-4">
              <JoinHistory squadId={squadId} />
            </TabsContent>
          </Tabs>
        ) : (
          <MemberList
            members={currentMembers}
            squadId={squad.id}
            squadLeaderId={squad.leaderId}
            isCurrentUserSquadLead={isSquadLead}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={paginate}
            onMemberRemoved={() => {
              // The squad data will automatically update via realtime subscription
              // which will trigger the useEffect to refetch members
              // No manual refresh needed
            }}
          />
        )}
      </div>
    </ErrorBoundary>
  )
}
